import 'dart:developer' as dev;
import 'dart:io';
import 'dart:math';
import 'package:cunning_document_scanner/cunning_document_scanner.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:hive/hive.dart';
import 'package:paperless_api/paperless_api.dart';
import 'package:paperless_mobile/constants.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/bloc/loading_status.dart';
import 'package:paperless_mobile/core/database/hive/hive_config.dart';
import 'package:paperless_mobile/core/database/tables/global_settings.dart';
import 'package:paperless_mobile/core/global/constants.dart';
import 'package:paperless_mobile/core/model/info_message_exception.dart';
import 'package:paperless_mobile/core/service/file_service.dart';
import 'package:paperless_mobile/features/app_drawer/view/app_drawer.dart';
import 'package:paperless_mobile/features/document_scan/cubit/document_scanner_cubit.dart';
import 'package:paperless_mobile/features/document_scan/view/widgets/export_scans_dialog.dart';
import 'package:paperless_mobile/features/document_scan/view/widgets/scanned_image_item.dart';
import 'package:paperless_mobile/features/document_search/view/sliver_search_bar.dart';
import 'package:paperless_mobile/features/document_upload/view/document_upload_preparation_page.dart';
import 'package:paperless_mobile/features/documents/view/pages/document_view.dart';
import 'package:paperless_mobile/features/landing/view/widgets/menu_search_user.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';
import 'package:paperless_mobile/helpers/connectivity_aware_action_wrapper.dart';
import 'package:paperless_mobile/helpers/message_helpers.dart';
import 'package:paperless_mobile/helpers/permission_helpers.dart';
import 'package:paperless_mobile/routing/routes/scanner_route.dart';
import 'package:paperless_mobile/routing/routes/shells/authenticated_route.dart';
import 'package:path/path.dart' as p;
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:permission_handler/permission_handler.dart';
import 'package:sliver_tools/sliver_tools.dart';

class ScannerPage extends StatefulWidget {
  const ScannerPage({super.key});

  @override
  State<ScannerPage> createState() => _ScannerPageState();
}

class _ScannerPageState extends State<ScannerPage>
    with SingleTickerProviderStateMixin {
  final SliverOverlapAbsorberHandle searchBarHandle =
      SliverOverlapAbsorberHandle();
  final SliverOverlapAbsorberHandle actionsHandle =
      SliverOverlapAbsorberHandle();

  final _scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: const AppDrawer(),
      floatingActionButton: FloatingActionButton(
        backgroundColor: AppColor.primary,
        heroTag: "fab_document_edit",
        shape: const CircleBorder(),
        onPressed: () => _openDocumentScanner(context),
        child: SvgPicture.asset('assets/svgs/camera.svg'),
      ),
      body: SafeArea(
        child: Column(
          children: [
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              child: MenuSearchUser(),
            ),
            _buildActions(),
            Expanded(
              child: BlocBuilder<DocumentScannerCubit, DocumentScannerState>(
                builder: (context, state) {
                  return switch (state.status) {
                    LoadingStatus.initial => _buildEmptyState(),
                    LoadingStatus.loading =>
                      const Center(child: Text("Restoring...")),
                    LoadingStatus.loaded => _buildImageGrid(state.scans),
                    LoadingStatus.error => const Placeholder(),
                  };
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActions() {
    return Column(
      children: [
        const Gap(10),
        SizedBox(
          height: 32,
          child: BlocBuilder<DocumentScannerCubit, DocumentScannerState>(
            builder: (context, state) {
              return ListView(
                controller: _scrollController,
                scrollDirection: Axis.horizontal,
                children: [
                  const SizedBox(width: 16),
                  GestureDetector(
                    onTap: state.scans.isNotEmpty
                        ? () => Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (context) => DocumentView(
                                  bytes: _assembleFileBytes(
                                    state.scans,
                                    forcePdf: true,
                                  ).then((file) => file.bytes),
                                  title: 'Back',
                                ),
                              ),
                            )
                        : null,
                    child: Container(
                      height: 32,
                      alignment: Alignment.center,
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        color: AppColor.white,
                      ),
                      child: Row(
                        children: [
                          SvgPicture.asset(
                            'assets/svgs/eye.svg',
                            colorFilter: ColorFilter.mode(
                                state.scans.isNotEmpty
                                    ? AppColor.primary
                                    : AppColor.grey_A1A1A1,
                                BlendMode.srcIn),
                          ),
                          const Gap(10),
                          Text(
                            S.of(context)!.previewScan,
                            style: TextStyle(
                              color: state.scans.isNotEmpty
                                  ? AppColor.primary
                                  : AppColor.grey_A1A1A1,
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  GestureDetector(
                    onTap: state.scans.isEmpty ? null : () => _reset(context),
                    child: Container(
                      height: 32,
                      alignment: Alignment.center,
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        color: AppColor.white,
                      ),
                      child: Row(
                        children: [
                          SvgPicture.asset(
                            'assets/svgs/trash.svg',
                            colorFilter: ColorFilter.mode(
                                state.scans.isNotEmpty
                                    ? AppColor.primary
                                    : AppColor.grey_A1A1A1,
                                BlendMode.srcIn),
                          ),
                          const Gap(10),
                          Text(
                            S.of(context)!.clearAll,
                            style: TextStyle(
                              color: state.scans.isNotEmpty
                                  ? AppColor.primary
                                  : AppColor.grey_A1A1A1,
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  GestureDetector(
                    onTap: state.scans.isEmpty
                        ? null
                        : () => _onPrepareDocumentUpload(context, state.scans),
                    child: Container(
                      height: 32,
                      alignment: Alignment.center,
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        color: AppColor.white,
                      ),
                      child: Row(
                        children: [
                          SvgPicture.asset(
                            'assets/svgs/upload.svg',
                            colorFilter: ColorFilter.mode(
                                state.scans.isNotEmpty
                                    ? AppColor.primary
                                    : AppColor.grey_A1A1A1,
                                BlendMode.srcIn),
                          ),
                          const Gap(10),
                          Text(
                            S.of(context)!.upload,
                            style: TextStyle(
                              color: state.scans.isNotEmpty
                                  ? AppColor.primary
                                  : AppColor.grey_A1A1A1,
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  GestureDetector(
                    onTap: state.scans.isEmpty ? null : _onSaveToFile,
                    child: Container(
                      height: 32,
                      alignment: Alignment.center,
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        color: AppColor.white,
                      ),
                      child: Row(
                        children: [
                          SvgPicture.asset(
                            'assets/svgs/export.svg',
                            colorFilter: ColorFilter.mode(
                                state.scans.isNotEmpty
                                    ? AppColor.primary
                                    : AppColor.grey_A1A1A1,
                                BlendMode.srcIn),
                          ),
                          const Gap(10),
                          Text(
                            S.of(context)!.export,
                            style: TextStyle(
                              color: state.scans.isNotEmpty
                                  ? AppColor.primary
                                  : AppColor.grey_A1A1A1,
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                ],
              );
            },
          ),
        ),
        const Gap(20),
        Container(
          height: 1,
          width: MediaQuery.of(context).size.width,
          color: AppColor.black_3C3C43.withOpacity(0.36),
        )
      ],
    );
  }

  void _onSaveToFile() async {
    final fileName = await showDialog<String>(
      context: context,
      builder: (context) => const ExportScansDialog(),
    );
    if (fileName != null) {
      final cubit = BlocProvider.of<DocumentScannerCubit>(context);
      final file = await _assembleFileBytes(
        forcePdf: true,
        BlocProvider.of<DocumentScannerCubit>(context).state.scans,
      );
      try {
        final globalSettings =
            Hive.box<GlobalSettings>(HiveBoxes.globalSettings).getValue()!;
        if (Platform.isAndroid && androidInfo!.version.sdkInt <= 29) {
          final isGranted = await askForPermission(Permission.storage);
          if (!isGranted) {
            showSnackBar(
              context,
              "Please grant Paperless Mobile permissions to access your filesystem.",
              action: SnackBarActionConfig(
                label: "OK",
                onPressed: openAppSettings,
              ),
            );
            return;
          }
        }
        await cubit.saveToFile(
          file.bytes,
          "$fileName.pdf",
          globalSettings.preferredLocaleSubtag,
        );
      } catch (error) {
        showGenericError(context, error);
      }
    }
  }

  Future<void> _openDocumentScanner(BuildContext context) async {
    final status = await Permission.camera.request();

    if (status == PermissionStatus.granted) {
      try {
        final imagesPath = await CunningDocumentScanner.getPictures(
          noOfPages: 1,
        );
        print(imagesPath?.first);

        if (imagesPath != null && imagesPath.isNotEmpty) {
          for (var element in imagesPath) {
            context.read<DocumentScannerCubit>().addScan(File(element));
          }
        }
      } on PlatformException {
        // 'Failed to get document path or operation cancelled!';
      }
    } else if (status == PermissionStatus.permanentlyDenied) {
      // Gợi ý mở settings nếu bị từ chối vĩnh viễn
      await openAppSettings();
    }
  }

  void _onPrepareDocumentUpload(BuildContext context, List<File> scans) async {
    final file = await _assembleFileBytes(
      scans,
      forcePdf: Hive.box<GlobalSettings>(HiveBoxes.globalSettings)
          .getValue()!
          .enforceSinglePagePdfUpload,
    );
    // ignore: use_build_context_synchronously
    DocumentUploadRoute(
      $extra: file.bytes,
      fileExtension: file.extension,
    ).push<DocumentUploadResult>(context);

    // ignore: use_build_context_synchronously
    BlocProvider.of<DocumentScannerCubit>(context).reset();
  }

  Widget _buildEmptyState() {
    return Column(
      children: [
        const Gap(60),
        Text(
          S.of(context)!.noDocumentsScannedYet,
          textAlign: TextAlign.center,
        ),
        const Gap(10),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset('assets/svgs/scanner_active.svg'),
            TextButton(
              child: Text(S.of(context)!.scanADocument),
              onPressed: () => _openDocumentScanner(context),
            ),
          ],
        ),
        const Gap(6),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              height: 0.5,
              width: 80,
              color: AppColor.grey_A1A1A1,
            ),
            const Gap(8),
            Text(
              S.of(context)!.or.toUpperCase(),
              style: const TextStyle(color: AppColor.grey_A1A1A1),
            ),
            const Gap(8),
            Container(
              height: 0.5,
              width: 80,
              color: AppColor.grey_A1A1A1,
            ),
          ],
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
          child: DottedBorder(
            color: AppColor.primary,
            strokeWidth: 1,
            dashPattern: const [6, 6],
            child: SizedBox(
              height: 160,
              width: MediaQuery.of(context).size.width,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  SvgPicture.asset('assets/svgs/browseFile.svg'),
                  Text(S.of(context)!.uploadADocumentFromThisDevice),
                  IntrinsicWidth(
                    child: GestureDetector(
                      onTap: _onUploadFromFilesystem,
                      child: Container(
                        height: 30,
                        alignment: Alignment.center,
                        padding: const EdgeInsets.symmetric(horizontal: 14),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(color: AppColor.primary)),
                        child: Text(S.of(context)!.browseFiles),
                      ),
                    ),
                  )
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildImageGrid(List<File> scans) {
    return CustomScrollView(
      key: const PageStorageKey('scanner_grid'),
      slivers: [
        // SliverOverlapInjector(handle: searchBarHandle),
        SliverPadding(
          padding: const EdgeInsets.all(8.0),
          sliver: SliverGrid.builder(
            itemCount: scans.length,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              childAspectRatio: 1 / sqrt(2),
              crossAxisSpacing: 10,
              mainAxisSpacing: 10,
            ),
            itemBuilder: (context, index) {
              return ScannedImageItem(
                file: scans[index],
                onDelete: () async {
                  try {
                    BlocProvider.of<DocumentScannerCubit>(context)
                        .removeScan(scans[index]);
                  } on PaperlessApiException catch (error, stackTrace) {
                    showErrorMessage(context, error, stackTrace);
                  } on InfoMessageException catch (error, stackTrace) {
                    showInfoMessage(context, error, stackTrace);
                  }
                },
                index: index,
                totalNumberOfFiles: scans.length,
              );
            },
          ),
        ),
      ],
    );
  }

  void _reset(BuildContext context) {
    try {
      BlocProvider.of<DocumentScannerCubit>(context).reset();
    } on PaperlessApiException catch (error, stackTrace) {
      showErrorMessage(context, error, stackTrace);
    }
  }

  void _onUploadFromFilesystem() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions:
          supportedFileExtensions.map((e) => e.replaceAll(".", "")).toList(),
      withData: true,
      allowMultiple: false,
    );
    if (result?.files.single.path != null) {
      final path = result!.files.single.path!;
      final extension = p.extension(path);
      final filename = p.basenameWithoutExtension(path);
      File file = File(path);
      if (!supportedFileExtensions.contains(extension.toLowerCase())) {
        showErrorMessage(
          context,
          const PaperlessApiException(ErrorCode.unsupportedFileFormat),
        );
        return;
      }
      DocumentUploadRoute(
        $extra: file.readAsBytesSync(),
        filename: filename,
        title: filename,
        fileExtension: extension,
      ).push<DocumentUploadResult>(context);
    }
  }

  ///
  /// Returns the file bytes of either a single file or multiple images concatenated into a single pdf.
  ///
  Future<AssembledFile> _assembleFileBytes(
    final List<File> files, {
    bool forcePdf = false,
  }) async {
    assert(files.isNotEmpty);
    if (files.length == 1 && !forcePdf) {
      final ext = p.extension(files.first.path);
      return AssembledFile(ext, files.first.readAsBytesSync());
    }
    final doc = pw.Document();
    for (final file in files) {
      final img = pw.MemoryImage(file.readAsBytesSync());
      doc.addPage(
        pw.Page(
          pageFormat: PdfPageFormat(
            img.width!.toDouble(),
            img.height!.toDouble(),
          ),
          build: (context) => pw.Image(img),
        ),
      );
    }
    return AssembledFile('.pdf', await doc.save());
  }
}

class AssembledFile {
  final String extension;
  final Uint8List bytes;

  AssembledFile(this.extension, this.bytes);
}
