import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/text_style/app_text_style.dart';
import 'package:paperless_mobile/features/document_upload/cubit/document_upload_cubit.dart';
import 'package:paperless_mobile/features/document_upload/cubit/document_upload_state.dart';
import 'package:paperless_mobile/features/document_upload/view/users_page.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';

class SelectDepartmentPage extends StatefulWidget {
  SelectDepartmentPage(
      {super.key,
      required this.documentUploadCubit,
      this.updateDepartment,
      this.isShowBottom = false});
  final DocumentUploadCubit documentUploadCubit;
  final bool isShowBottom;
  VoidCallback? updateDepartment;

  @override
  State<SelectDepartmentPage> createState() => _SelectDepartmentPageState();
}

class _SelectDepartmentPageState extends State<SelectDepartmentPage> {
  bool isSelected = false;

  void checkSelected() {
    final selectedDepartments =
        widget.documentUploadCubit.state.departments.where((e) => e.isSelected);
    if (selectedDepartments.isNotEmpty) {
      setState(() {
        isSelected = true;
      });
    } else {
      setState(() {
        isSelected = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;

    return BlocProvider.value(
      value: widget.documentUploadCubit,
      child: Scaffold(
        appBar: AppBar(
          iconTheme: const IconThemeData(color: AppColor.primary),
          title: Row(
            children: [
              Text(
                S.of(context)!.selectDepartments,
                style: AppTextStyles.textStyleAppBar,
              )
            ],
          ),
        ),
        bottomNavigationBar: widget.isShowBottom
            ? Padding(
                padding: const EdgeInsets.only(bottom: 30, left: 16, right: 16),
                child: GestureDetector(
                  onTap: () {
                    if (isSelected) {
                      Navigator.pop(context);
                      widget.updateDepartment?.call();
                    }
                  },
                  child: Container(
                    alignment: Alignment.center,
                    height: 48,
                    decoration: BoxDecoration(
                        color: isSelected
                            ? AppColor.primary
                            : AppColor.grey_DADADA,
                        borderRadius: BorderRadius.circular(10)),
                    child: Text(
                      S.of(context)!.save,
                      style: AppTextStyles.textStyle14.copyWith(
                          color: AppColor.white, fontWeight: FontWeight.w600),
                    ),
                  ),
                ),
              )
            : const SizedBox.shrink(),
        body: Padding(
            padding: const EdgeInsets.only(top: 24, left: 16, right: 16),
            child: BlocBuilder<DocumentUploadCubit, DocumentUploadState>(
              builder: (context, state) {
                return ListView.separated(
                  itemBuilder: (context, index) {
                    final department = state.departments[index];
                    return GestureDetector(
                      onTap: () {
                        widget.documentUploadCubit.selectedDepartment(index);
                        checkSelected();
                      },
                      child: Container(
                        height: 46,
                        width: size.width,
                        padding: const EdgeInsets.symmetric(horizontal: 14),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          color: department.isSelected
                              ? AppColor.blue_024CAA.withOpacity(0.1)
                              : AppColor.white,
                        ),
                        child: Row(
                          children: [
                            Text(
                              department.name,
                              style: AppTextStyles.textStyle14
                                  .copyWith(fontWeight: FontWeight.w500),
                            ),
                            const Spacer(),
                            if (department.isSelected)
                              SvgPicture.asset('assets/svgs/tick.svg'),
                          ],
                        ),
                      ),
                    );
                  },
                  separatorBuilder: (context, index) => const Gap(16),
                  itemCount: state.departments.length,
                );
              },
            )),
      ),
    );
  }
}
