// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:paperless_mobile/features/documents/view/pages/documents_page.dart';

// class DocumentPageProvider extends StatelessWidget {
//   const DocumentPageProvider({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return BlocProvider(
//       create: (context) => SubjectBloc(),
//       child: const DocumentsPage(),
//     );
//   }
// }
