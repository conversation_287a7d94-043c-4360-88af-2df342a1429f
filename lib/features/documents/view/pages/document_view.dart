import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/extensions/flutter_extensions.dart';
import 'package:paperless_mobile/core/text_style/app_text_style.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';
import 'package:pdfx/pdfx.dart';

class DocumentView extends StatefulWidget {
  final Future<Uint8List> bytes;
  final String? title;
  final bool showAppBar;
  final bool showControls;
  const DocumentView({
    super.key,
    required this.bytes,
    this.showAppBar = true,
    this.showControls = true,
    this.title,
  });

  @override
  State<DocumentView> createState() => _DocumentViewState();
}

class _DocumentViewState extends State<DocumentView> {
  late final PdfController _controller;
  late final Future<PdfDocument> _documentFuture;
  late final Completer<PdfDocument> _documentCompleter;
  int _currentPage = 1;
  int? _totalPages;
  bool _isDisposed = false;

  @override
  void initState() {
    super.initState();
    _documentCompleter = Completer<PdfDocument>();
    _documentFuture = _documentCompleter.future;
    _controller = PdfController(document: _documentFuture);

    // Load document with cancellation support
    _loadDocument();
  }

  void _loadDocument() async {
    try {
      final bytes = await widget.bytes;
      if (!_isDisposed && !_documentCompleter.isCompleted) {
        final document = await PdfDocument.openData(bytes);
        if (!_isDisposed && !_documentCompleter.isCompleted) {
          _documentCompleter.complete(document);
        }
      }
    } catch (error) {
      if (!_isDisposed && !_documentCompleter.isCompleted) {
        _documentCompleter.completeError(error);
      }
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    if (!_documentCompleter.isCompleted) {
      _documentCompleter.completeError('Document loading cancelled');
    }
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final pageTransitionDuration = MediaQuery.disableAnimationsOf(context)
        ? 0.milliseconds
        : 100.milliseconds;
    final canGoToNextPage = _totalPages != null && _currentPage < _totalPages!;
    final canGoToPreviousPage =
        _controller.pagesCount != null && _currentPage > 1;
    return Scaffold(
      appBar: widget.showAppBar
          ? AppBar(
              title: widget.title != null
                  ? Row(
                      children: [
                        Expanded(
                          child: Text(widget.title!,
                              overflow: TextOverflow.ellipsis,
                              style: AppTextStyles.textStyleAppBar),
                        ),
                      ],
                    )
                  : null,
              iconTheme: const IconThemeData(color: AppColor.primary),
            )
          : null,
      bottomNavigationBar: widget.showControls
          ? BottomAppBar(
              child: Row(
                children: [
                  Flexible(
                    child: Row(
                      children: [
                        IconButton.filled(
                          onPressed: canGoToPreviousPage
                              ? () async {
                                  await _controller.previousPage(
                                    duration: pageTransitionDuration,
                                    curve: Curves.easeOut,
                                  );
                                }
                              : null,
                          icon: const Icon(
                            Icons.arrow_left,
                            color: AppColor.white,
                          ),
                          style: ButtonStyle(
                              backgroundColor:
                                  MaterialStateProperty.all(AppColor.primary)),
                        ),
                        const SizedBox(width: 16),
                        IconButton.filled(
                          onPressed: canGoToNextPage
                              ? () async {
                                  await _controller.nextPage(
                                    duration: pageTransitionDuration,
                                    curve: Curves.easeOut,
                                  );
                                }
                              : null,
                          style: ButtonStyle(
                              backgroundColor:
                                  MaterialStateProperty.all(AppColor.primary)),
                          icon: const Icon(
                            Icons.arrow_right,
                            color: AppColor.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                  PdfPageNumber(
                    controller: _controller,
                    builder: (context, loadingState, page, pagesCount) {
                      if (loadingState != PdfLoadingState.success) {
                        return const Text("-/-");
                      }
                      return Text(
                        "$page/$pagesCount",
                        style: Theme.of(context).textTheme.titleMedium,
                      ).padded();
                    },
                  ),
                ],
              ),
            )
          : null,
      body: FutureBuilder<PdfDocument>(
        future: _documentFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Show loading indicator while loading
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    color: AppColor.primary,
                  ),
                ],
              ),
            );
          } else if (snapshot.hasError) {
            // Show error if any (but not if it was cancelled)
            final errorMessage = snapshot.error.toString();
            if (errorMessage.contains('Document loading cancelled')) {
              // If loading was cancelled, show a simple message
              return Center(
                child: Text(
                  S.of(context)!.loadingCancelled,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                ),
              );
            }

            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    S.of(context)!.errorLoadingDocument,
                    style: AppTextStyles.textStyleAppBar.copyWith(
                      color: Colors.red,
                    ),
                  ),
                  const SizedBox(height: 8),
                ],
              ),
            );
          } else {
            // Show PDF when loaded
            return PdfView(
              controller: _controller,
              onDocumentLoaded: (document) {
                if (mounted) {
                  setState(() {
                    _totalPages = document.pagesCount;
                  });
                }
              },
              onPageChanged: (page) {
                if (mounted) {
                  setState(() {
                    _currentPage = page;
                  });
                }
              },
            );
          }
        },
      ),
    );
  }
}
