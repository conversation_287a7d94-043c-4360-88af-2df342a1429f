import 'package:flutter/material.dart';
import 'package:paperless_api/paperless_api.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/widgets/dialog_utils/dialog_cancel_button.dart';
import 'package:paperless_mobile/core/widgets/dialog_utils/dialog_confirm_button.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';

class DeleteDocumentConfirmationDialog extends StatelessWidget {
  final DocumentModel document;
  const DeleteDocumentConfirmationDialog({super.key, required this.document});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      surfaceTintColor: Colors.transparent,
      title: Text(S.of(context)!.confirmDeletion),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            S.of(context)!.areYouSureYouWantToDeleteTheFollowingDocuments(1),
          ),
          const SizedBox(height: 16),
          Text(
            document.title.isEmpty ? '(no title)' : document.title,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: const TextStyle(
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: 16),
          Text(S.of(context)!.thisActionIsIrreversibleDoYouWishToProceedAnyway),
        ],
      ),
      actions: [
        const DialogCancelButton(),
        ElevatedButton(
          onPressed: () => Navigator.of(context).pop(true),
          style: ElevatedButton.styleFrom(backgroundColor: AppColor.red_FA0A02),
          child: Text(
            S.of(context)!.delete,
            style: const TextStyle(color: AppColor.white),
          ),
        ),
        
      ],
    );
  }
}
