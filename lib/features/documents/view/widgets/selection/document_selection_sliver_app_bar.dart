import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:hive/hive.dart';
import 'package:paperless_api/paperless_api.dart';
import 'package:paperless_mobile/constants.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/database/hive/hive_config.dart';
import 'package:paperless_mobile/core/database/tables/global_settings.dart';
import 'package:paperless_mobile/core/database/tables/local_user_account.dart';
import 'package:paperless_mobile/core/extensions/flutter_extensions.dart';
import 'package:paperless_mobile/core/notifier/document_changed_notifier.dart';
import 'package:paperless_mobile/core/security/session_manager.dart';
import 'package:paperless_mobile/features/document_details/cubit/document_details_cubit.dart';
import 'package:paperless_mobile/features/document_details/view/dialogs/select_file_type_dialog.dart';
import 'package:paperless_mobile/features/notifications/services/local_notification_service.dart';
import 'package:paperless_mobile/features/document_edit/cubit/document_edit_cubit.dart';
import 'package:paperless_mobile/features/document_upload/cubit/document_upload_cubit.dart';
import 'package:paperless_mobile/features/document_upload/view/permissions_page.dart';
import 'package:paperless_mobile/features/document_upload/view/select_department_page.dart';
import 'package:paperless_mobile/features/document_upload/view/select_project_page.dart';
import 'package:paperless_mobile/features/documents/cubit/documents_cubit.dart';
import 'package:paperless_mobile/features/documents/view/widgets/selection/bulk_delete_confirmation_dialog.dart';
import 'package:paperless_mobile/features/documents/view/widgets/selection/bulk_document_page.dart';
import 'package:paperless_mobile/features/settings/model/file_download_type.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';
import 'package:paperless_mobile/helpers/message_helpers.dart';
import 'package:paperless_mobile/helpers/permission_helpers.dart';
import 'package:paperless_mobile/routing/routes/documents_route.dart';
import 'package:permission_handler/permission_handler.dart';

class DocumentSelectionSliverAppBar extends StatelessWidget {
  final DocumentsState state;
  const DocumentSelectionSliverAppBar({super.key, required this.state});

  @override
  Widget build(BuildContext context) {
    final documentEditCubit = DocumentEditCubit(
      context.read(),
      context.read(),
      context.read(),
      document: state.selection.first,
    );
    return SliverAppBar(
      stretch: false,
      pinned: true,
      floating: true,
      snap: true,
      backgroundColor: AppColor.backgroundColor,
      title: Row(
        children: [
          Text(
            S.of(context)!.countSelected(state.selection.length),
          ),
        ],
      ),
      leading: IconButton(
        icon: const Icon(Icons.close),
        onPressed: () => context.read<DocumentsCubit>().resetSelection(),
      ),
      actions: [
        IconButton(
          icon: SvgPicture.asset('assets/svgs/edit.svg'),
          onPressed: () {
            onDownloadDocuments(context);
          },
        ),
      ],
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(kTextTabBarHeight),
        child: SizedBox(
          height: kTextTabBarHeight,
          child: ListView(
            scrollDirection: Axis.horizontal,
            children: [
              ActionChip(
                backgroundColor: AppColor.white,
                side: const BorderSide(color: AppColor.primary),
                label: Text(S.of(context)!.departments),
                avatar: SvgPicture.asset('assets/svgs/edit.svg'),
                onPressed: () {
                  final documentUploadCubit = DocumentUploadCubit(
                    context.read(),
                    context.read(),
                    context.read(),
                    context.read(),
                    PaperlessTasksApiImpl(
                        context.read<SessionManager>().client),
                  )..getAllDepartment();

                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => BlocProvider.value(
                        value: documentUploadCubit,
                        child: SelectDepartmentPage(
                            updateDepartment: () {
                              final List<int> departmentSelected =
                                  documentUploadCubit.state.departments
                                      .where((e) => e.isSelected)
                                      .map((e) => e.id)
                                      .toList();
                              for (var element in state.selection) {
                                final item = element.copyWith(
                                    departments: departmentSelected);
                                documentEditCubit.updateDocument(item);
                              }
                            },
                            isShowBottom: true,
                            documentUploadCubit: documentUploadCubit),
                      ),
                    ),
                  );
                },
              ).paddedOnly(left: 8, right: 4),
              ActionChip(
                backgroundColor: AppColor.white,
                side: const BorderSide(color: AppColor.primary),
                label: Text(S.of(context)!.projects),
                avatar: SvgPicture.asset('assets/svgs/edit.svg'),
                onPressed: () {
                  final documentUploadCubit = DocumentUploadCubit(
                    context.read(),
                    context.read(),
                    context.read(),
                    context.read(),
                    PaperlessTasksApiImpl(
                        context.read<SessionManager>().client),
                  )..getAllProject();

                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => BlocProvider.value(
                        value: documentUploadCubit,
                        child: SelectProjectPage(
                            updateProject: () {
                              final List<int> projectSelected =
                                  documentUploadCubit.state.projects
                                      .where((e) => e.isSelected)
                                      .map((e) => e.id)
                                      .toList();
                              for (var element in state.selection) {
                                final item =
                                    element.copyWith(projects: projectSelected);
                                documentEditCubit.updateDocument(item);
                              }
                            },
                            isShowBottom: true,
                            documentUploadCubit: documentUploadCubit),
                      ),
                    ),
                  );
                },
              ).paddedOnly(left: 8, right: 4),
              ActionChip(
                backgroundColor: AppColor.white,
                side: const BorderSide(color: AppColor.primary),
                label: Text(S.of(context)!.correspondent),
                avatar: SvgPicture.asset('assets/svgs/edit.svg'),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => BulkDocumentPage(
                        $extra: BulkEditExtraWrapper(
                          state.selection,
                          LabelType.correspondent,
                        ),
                      ),
                    ),
                  );
                },
              ).paddedOnly(left: 8, right: 4),
              ActionChip(
                backgroundColor: AppColor.white,
                side: const BorderSide(color: AppColor.primary),
                label: Text(S.of(context)!.documentType),
                avatar: SvgPicture.asset('assets/svgs/edit.svg'),
                onPressed: () async {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => BulkDocumentPage(
                        $extra: BulkEditExtraWrapper(
                          state.selection,
                          LabelType.documentType,
                        ),
                      ),
                    ),
                  );
                },
              ).paddedOnly(left: 8, right: 4),
              ActionChip(
                backgroundColor: AppColor.white,
                side: const BorderSide(color: AppColor.primary),
                label: Text(S.of(context)!.storagePath),
                avatar: SvgPicture.asset('assets/svgs/edit.svg'),
                onPressed: () async {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => BulkDocumentPage(
                        $extra: BulkEditExtraWrapper(
                          state.selection,
                          LabelType.storagePath,
                        ),
                      ),
                    ),
                  );
                },
              ).paddedOnly(left: 8, right: 4),
              _buildBulkEditTagsChip(context).paddedOnly(left: 4, right: 4),
              ActionChip(
                backgroundColor: AppColor.white,
                side: const BorderSide(color: AppColor.primary),
                label: Text(S.of(context)!.permissions),
                avatar: SvgPicture.asset('assets/svgs/edit.svg'),
                onPressed: () {
                  final documentUploadCubit = DocumentUploadCubit(
                    context.read(),
                    context.read(),
                    context.read(),
                    context.read(),
                    PaperlessTasksApiImpl(
                        context.read<SessionManager>().client),
                  )
                    ..getGroups()
                    ..getUser();

                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => BlocProvider.value(
                        value: documentUploadCubit,
                        child: PermissionsPage(
                            isShowBottom: true,
                            updatePermission: () {
                              for (var element in state.selection) {
                                documentUploadCubit
                                    .addPermission(element.id.toString());
                              }
                            },
                            documentUploadCubit: documentUploadCubit),
                      ),
                    ),
                  );
                },
              ).paddedOnly(left: 8, right: 4),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBulkEditTagsChip(BuildContext context) {
    return ActionChip(
      backgroundColor: AppColor.white,
      side: const BorderSide(color: AppColor.primary),
      label: Text(S.of(context)!.tags),
      avatar: SvgPicture.asset('assets/svgs/edit.svg'),
      onPressed: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => BulkDocumentPage(
              $extra: BulkEditExtraWrapper(
                state.selection,
                LabelType.tag,
              ),
            ),
          ),
        );
      },
    );
  }

  void onDownloadDocuments(BuildContext context) async {
    // Get dependencies before any async operations
    final userId = context.read<LocalUserAccount>().id;
    final api = context.read<PaperlessDocumentsApi>();
    final notifier = context.read<DocumentChangedNotifier>();
    final notificationService = context.read<LocalNotificationService>();

    try {
      final globalSettings =
          Hive.box<GlobalSettings>(HiveBoxes.globalSettings).getValue()!;
      bool original;

      switch (globalSettings.defaultDownloadType) {
        case FileDownloadType.original:
          original = true;
          break;
        case FileDownloadType.archived:
          original = false;
          break;
        case FileDownloadType.alwaysAsk:
          final isOriginal = await showDialog<bool>(
            context: context,
            builder: (context) => SelectFileTypeDialog(
              onRememberSelection: (downloadType) {
                globalSettings.defaultDownloadType = downloadType;
                globalSettings.save();
              },
            ),
          );
          if (isOriginal == null) {
            return;
          } else {
            original = isOriginal;
          }
          break;
      }

      if (Platform.isAndroid && androidInfo!.version.sdkInt <= 29) {
        final isGranted = await askForPermission(Permission.storage);
        if (!isGranted) {
          return;
          //TODO: Ask user to grant permissions
        }
      }

      for (var element in state.selection) {
        final documentDetailsCubit = DocumentDetailsCubit(
          api,
          notifier,
          notificationService,
          id: element.id,
        );
        // Initialize cubit to load document data first
        await documentDetailsCubit.initialize();
        documentDetailsCubit.downloadDocumentInId(
          id: element.id,
          downloadOriginal: original,
          locale: globalSettings.preferredLocaleSubtag,
          userId: userId,
        );
      }
    } on PaperlessApiException catch (error, stackTrace) {
      if (context.mounted) {
        showErrorMessage(context, error, stackTrace);
      }
    } catch (error) {
      if (context.mounted) {
        showGenericError(context, error);
      }
    }
  }
}
