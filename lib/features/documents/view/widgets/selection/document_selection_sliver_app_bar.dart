import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:paperless_api/paperless_api.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/extensions/flutter_extensions.dart';
import 'package:paperless_mobile/core/security/session_manager.dart';
import 'package:paperless_mobile/features/document_edit/cubit/document_edit_cubit.dart';
import 'package:paperless_mobile/features/document_upload/cubit/document_upload_cubit.dart';
import 'package:paperless_mobile/features/document_upload/view/permissions_page.dart';
import 'package:paperless_mobile/features/document_upload/view/select_department_page.dart';
import 'package:paperless_mobile/features/document_upload/view/select_project_page.dart';
import 'package:paperless_mobile/features/documents/cubit/documents_cubit.dart';
import 'package:paperless_mobile/features/documents/view/widgets/selection/bulk_delete_confirmation_dialog.dart';
import 'package:paperless_mobile/features/documents/view/widgets/selection/bulk_document_page.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';
import 'package:paperless_mobile/helpers/message_helpers.dart';
import 'package:paperless_mobile/routing/routes/documents_route.dart';
import 'package:paperless_mobile/routing/routes/shells/authenticated_route.dart';

class DocumentSelectionSliverAppBar extends StatelessWidget {
  final DocumentsState state;
  const DocumentSelectionSliverAppBar({super.key, required this.state});

  @override
  Widget build(BuildContext context) {
    final documentEditCubit = DocumentEditCubit(
      context.read(),
      context.read(),
      context.read(),
      document: state.selection.first,
    );
    return SliverAppBar(
      stretch: false,
      pinned: true,
      floating: true,
      snap: true,
      backgroundColor: AppColor.backgroundColor,
      title: Row(
        children: [
          Text(
            S.of(context)!.countSelected(state.selection.length),
          ),
        ],
      ),
      leading: IconButton(
        icon: const Icon(Icons.close),
        onPressed: () => context.read<DocumentsCubit>().resetSelection(),
      ),
      actions: [
        IconButton(
          icon: SvgPicture.asset('assets/svgs/trash.svg'),
          onPressed: () async {
            final shouldDelete = await showDialog<bool>(
                  context: context,
                  builder: (context) =>
                      BulkDeleteConfirmationDialog(state: state),
                ) ??
                false;
            if (shouldDelete) {
              try {
                await context
                    .read<DocumentsCubit>()
                    .bulkDelete(state.selection);
                showSnackBar(
                  context,
                  S.of(context)!.documentsSuccessfullyDeleted,
                );
                context.read<DocumentsCubit>().resetSelection();
              } on PaperlessApiException catch (error, stackTrace) {
                showErrorMessage(context, error, stackTrace);
              }
            }
          },
        ),
      ],
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(kTextTabBarHeight),
        child: SizedBox(
          height: kTextTabBarHeight,
          child: ListView(
            scrollDirection: Axis.horizontal,
            children: [
              ActionChip(
                backgroundColor: AppColor.white,
                side: const BorderSide(color: AppColor.primary),
                label: Text(S.of(context)!.departments),
                avatar: SvgPicture.asset('assets/svgs/edit.svg'),
                onPressed: () {
                  final documentUploadCubit = DocumentUploadCubit(
                    context.read(),
                    context.read(),
                    context.read(),
                    context.read(),
                    PaperlessTasksApiImpl(
                        context.read<SessionManager>().client),
                  )..getAllDepartment();

                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => BlocProvider.value(
                        value: documentUploadCubit,
                        child: SelectDepartmentPage(
                            updateDepartment: () {
                              final List<int> departmentSelected =
                                  documentUploadCubit.state.departments
                                      .where((e) => e.isSelected)
                                      .map((e) => e.id)
                                      .toList();
                              for (var element in state.selection) {
                                final item = element.copyWith(
                                    departments: departmentSelected);
                                documentEditCubit.updateDocument(item);
                              }
                            },
                            isShowBottom: true,
                            documentUploadCubit: documentUploadCubit),
                      ),
                    ),
                  );
                },
              ).paddedOnly(left: 8, right: 4),
              ActionChip(
                backgroundColor: AppColor.white,
                side: const BorderSide(color: AppColor.primary),
                label: Text(S.of(context)!.projects),
                avatar: SvgPicture.asset('assets/svgs/edit.svg'),
                onPressed: () {
                  final documentUploadCubit = DocumentUploadCubit(
                    context.read(),
                    context.read(),
                    context.read(),
                    context.read(),
                    PaperlessTasksApiImpl(
                        context.read<SessionManager>().client),
                  )..getAllProject();

                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => BlocProvider.value(
                        value: documentUploadCubit,
                        child: SelectProjectPage(
                            updateProject: () {
                              final List<int> projectSelected =
                                  documentUploadCubit.state.projects
                                      .where((e) => e.isSelected)
                                      .map((e) => e.id)
                                      .toList();
                              for (var element in state.selection) {
                                final item =
                                    element.copyWith(projects: projectSelected);
                                documentEditCubit.updateDocument(item);
                              }
                            },
                            isShowBottom: true,
                            documentUploadCubit: documentUploadCubit),
                      ),
                    ),
                  );
                },
              ).paddedOnly(left: 8, right: 4),
              ActionChip(
                backgroundColor: AppColor.white,
                side: const BorderSide(color: AppColor.primary),
                label: Text(S.of(context)!.correspondent),
                avatar: SvgPicture.asset('assets/svgs/edit.svg'),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => BulkDocumentPage(
                        $extra: BulkEditExtraWrapper(
                          state.selection,
                          LabelType.correspondent,
                        ),
                      ),
                    ),
                  );
                },
              ).paddedOnly(left: 8, right: 4),
              ActionChip(
                backgroundColor: AppColor.white,
                side: const BorderSide(color: AppColor.primary),
                label: Text(S.of(context)!.documentType),
                avatar: SvgPicture.asset('assets/svgs/edit.svg'),
                onPressed: () async {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => BulkDocumentPage(
                        $extra: BulkEditExtraWrapper(
                          state.selection,
                          LabelType.documentType,
                        ),
                      ),
                    ),
                  );
                },
              ).paddedOnly(left: 8, right: 4),
              ActionChip(
                backgroundColor: AppColor.white,
                side: const BorderSide(color: AppColor.primary),
                label: Text(S.of(context)!.storagePath),
                avatar: SvgPicture.asset('assets/svgs/edit.svg'),
                onPressed: () async {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => BulkDocumentPage(
                        $extra: BulkEditExtraWrapper(
                          state.selection,
                          LabelType.storagePath,
                        ),
                      ),
                    ),
                  );
                },
              ).paddedOnly(left: 8, right: 4),
              _buildBulkEditTagsChip(context).paddedOnly(left: 4, right: 4),
              ActionChip(
                backgroundColor: AppColor.white,
                side: const BorderSide(color: AppColor.primary),
                label: Text(S.of(context)!.permissions),
                avatar: SvgPicture.asset('assets/svgs/edit.svg'),
                onPressed: () {
                  final documentUploadCubit = DocumentUploadCubit(
                    context.read(),
                    context.read(),
                    context.read(),
                    context.read(),
                    PaperlessTasksApiImpl(
                        context.read<SessionManager>().client),
                  )
                    ..getGroups()
                    ..getUser();

                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => BlocProvider.value(
                        value: documentUploadCubit,
                        child: PermissionsPage(
                            isShowBottom: true,
                            updatePermission: () {
                              for (var element in state.selection) {
                                documentUploadCubit
                                    .addPermission(element.id.toString());
                              }
                            },
                            documentUploadCubit: documentUploadCubit),
                      ),
                    ),
                  );
                },
              ).paddedOnly(left: 8, right: 4),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBulkEditTagsChip(BuildContext context) {
    return ActionChip(
      backgroundColor: AppColor.white,
      side: const BorderSide(color: AppColor.primary),
      label: Text(S.of(context)!.tags),
      avatar: SvgPicture.asset('assets/svgs/edit.svg'),
      onPressed: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => BulkDocumentPage(
              $extra: BulkEditExtraWrapper(
                state.selection,
                LabelType.tag,
              ),
            ),
          ),
        );
      },
    );
  }
}
