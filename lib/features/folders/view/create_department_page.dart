import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:paperless_api/paperless_api.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/text_style/app_text_style.dart';
import 'package:paperless_mobile/features/document_upload/cubit/document_upload_cubit.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';

class CreateDepartmentPage extends StatefulWidget {
  const CreateDepartmentPage({
    super.key,
  });

  @override
  State<CreateDepartmentPage> createState() => _CreateDepartmentPageState();
}

class _CreateDepartmentPageState extends State<CreateDepartmentPage> {
  final TextEditingController _projectNameController = TextEditingController();

  @override
  initState() {
    super.initState();
    _projectNameController.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    _projectNameController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final DocumentUploadCubit documentUploadCubit =
        context.read<DocumentUploadCubit>();
    return Scaffold(
      appBar: AppBar(
        iconTheme: const IconThemeData(color: AppColor.primary),
        title: Row(
          children: [
            Text(
              S.of(context)!.addNewDepartment,
              style: AppTextStyles.textStyleAppBar,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.only(top: 24, left: 16, right: 16),
        child: Column(
          children: [
            Row(
              children: [
                Text(
                  S.of(context)!.departmentName,
                  style: AppTextStyles.textStyleBold14,
                ),
                const Text(
                  '*',
                  style: TextStyle(color: Colors.red),
                )
              ],
            ),
            const Gap(8),
            SizedBox(
              height: 48,
              child: TextField(
                controller: _projectNameController,
                decoration: InputDecoration(
                  fillColor: AppColor.white,
                  filled: true,
                  hintText: S.of(context)!.enterHere,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                    borderSide: BorderSide.none,
                  ),
                  // suffixIcon: const Icon(Icons.search),
                  hintStyle: const TextStyle(
                    color: AppColor.grey_909090,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.only(bottom: 30, left: 16, right: 16),
        child: GestureDetector(
          onTap: () async {
            Navigator.pop(context);
            await documentUploadCubit
                .createDepartment(_projectNameController.text.trim());
            await documentUploadCubit.getAllDepartment();
          },
          child: Container(
            alignment: Alignment.center,
            height: 48,
            decoration: BoxDecoration(
                color: _projectNameController.text.isEmpty
                    ? AppColor.grey_DADADA
                    : AppColor.primary,
                borderRadius: BorderRadius.circular(10)),
            child: Text(
              S.of(context)!.save,
              style: AppTextStyles.textStyle14
                  .copyWith(color: AppColor.white, fontWeight: FontWeight.w600),
            ),
          ),
        ),
      ),
    );
  }
}
