import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:paperless_api/paperless_api.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/text_style/app_text_style.dart';
import 'package:paperless_mobile/features/document_upload/cubit/document_upload_cubit.dart';
import 'package:paperless_mobile/features/document_upload/cubit/document_upload_state.dart';
import 'package:paperless_mobile/features/folders/view/create_department_page.dart';
import 'package:paperless_mobile/features/folders/view/create_project_page.dart';
import 'package:paperless_mobile/features/folders/view/department_detail_page.dart';
import 'package:paperless_mobile/features/folders/view/project_detail_page.dart';
import 'package:paperless_mobile/features/linked_documents/cubit/linked_documents_cubit.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';
import 'package:paperless_mobile/routing/routes/labels_route.dart';
import 'package:paperless_mobile/routing/routes/shells/authenticated_route.dart';

class FolderProjectPage extends StatefulWidget {
  const FolderProjectPage({super.key});

  @override
  State<FolderProjectPage> createState() => _FolderProjectPageState();
}

class _FolderProjectPageState extends State<FolderProjectPage> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    _searchController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final DocumentUploadCubit documentUploadCubit =
        context.read<DocumentUploadCubit>();
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Text(
              S.of(context)!.projects,
              style: AppTextStyles.textStyleAppBar,
            ),
          ],
        ),
        actions: [
          IconButton(
              onPressed: () {
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => BlocProvider.value(
                              value: documentUploadCubit,
                              child: const CreateProjectPage(),
                            )));
              },
              icon: SvgPicture.asset('assets/svgs/add.svg')),
          const Gap(8)
        ],
        iconTheme: const IconThemeData(color: AppColor.primary),
      ),
      body: BlocBuilder<DocumentUploadCubit, DocumentUploadState>(
        builder: (context, state) {
          return Padding(
            padding: const EdgeInsets.only(top: 24, left: 16, right: 16),
            child: Column(
              children: [
                SizedBox(
                  height: 48,
                  child: TextField(
                    controller: _searchController,
                    onChanged: (value) {
                      if (value.isEmpty) {
                        documentUploadCubit.getAllProject();
                        return;
                      }
                      documentUploadCubit.searchProject(value);
                    },
                    decoration: InputDecoration(
                      fillColor: AppColor.white,
                      filled: true,
                      hintText: S.of(context)!.searchProject,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                        borderSide: BorderSide.none,
                      ),
                      suffixIcon: const Icon(Icons.search),
                      hintStyle: const TextStyle(
                        color: AppColor.grey_909090,
                      ),
                    ),
                  ),
                ),
                const Gap(24),
                Expanded(
                    child: state.projects.isEmpty
                        ? Center(
                            child: Text(S.of(context)!.noProjectsFound),
                          )
                        : ListView.separated(
                            itemBuilder: (context, index) => InkWell(
                                  borderRadius: BorderRadius.circular(10),
                                  onTap: () {
                                    Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                            builder: (context) =>
                                                MultiBlocProvider(
                                                  providers: [
                                                    BlocProvider.value(
                                                      value:
                                                          documentUploadCubit,
                                                    ),
                                                    BlocProvider(
                                                      create: (context) =>
                                                          LinkedDocumentsCubit(
                                                        DocumentFilter(
                                                            projectsIdIn: [
                                                              state
                                                                  .projects[
                                                                      index]
                                                                  .id
                                                            ]),
                                                        context.read(),
                                                        context.read(),
                                                        context.read(),
                                                      ),
                                                    )
                                                  ],
                                                  child: ProjectDetailPage(
                                                      project: state
                                                          .projects[index]),
                                                )));
                                  },
                                  child: Container(
                                    constraints:
                                        const BoxConstraints(minHeight: 48),
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 14),
                                    decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(10),
                                        color: AppColor.white),
                                    child: Row(
                                      children: [
                                        Text(
                                          state.projects[index].name,
                                          overflow: TextOverflow.ellipsis,
                                          style: AppTextStyles.textStyle14
                                              .copyWith(
                                                  fontWeight: FontWeight.w500),
                                        ),
                                        const Spacer(),
                                        GestureDetector(
                                          behavior: HitTestBehavior.opaque,
                                          onTap: () {
                                            LinkedDocumentsRoute(DocumentFilter(
                                                projectsIdIn: [
                                                  state.projects[index].id
                                                ])).push(context);
                                          },
                                          child: Row(
                                            children: [
                                              Text(
                                                state.projects[index]
                                                    .documentCount
                                                    .toString(),
                                                style: AppTextStyles.textStyle14
                                                    .copyWith(
                                                        fontWeight:
                                                            FontWeight.w500,
                                                        color: state
                                                                    .projects[
                                                                        index]
                                                                    .documentCount >
                                                                0
                                                            ? AppColor.primary
                                                            : AppColor
                                                                .black_333333
                                                                .withOpacity(
                                                                    0.2)),
                                              ),
                                              const Gap(6),
                                              SvgPicture.asset(
                                                'assets/svgs/iconLink.svg',
                                                colorFilter: ColorFilter.mode(
                                                  state.projects[index]
                                                              .documentCount >
                                                          0
                                                      ? AppColor.primary
                                                      : AppColor.black_333333
                                                          .withOpacity(0.2),
                                                  BlendMode.srcIn,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        const Gap(22),
                                        Icon(
                                          Icons.chevron_right,
                                          color: AppColor.black_3C3C43
                                              .withOpacity(0.3),
                                        )
                                      ],
                                    ),
                                  ),
                                ),
                            separatorBuilder: (context, index) => const Gap(10),
                            itemCount: state.projects.length)),
              ],
            ),
          );
        },
      ),
    );
  }
}
