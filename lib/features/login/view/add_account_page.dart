import 'dart:async';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:paperless_api/paperless_api.dart';
import 'package:paperless_mobile/constants.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/exception/server_message_exception.dart';
import 'package:paperless_mobile/core/model/info_message_exception.dart';
import 'package:paperless_mobile/core/service/connectivity_status_service.dart';
import 'package:paperless_mobile/core/extensions/flutter_extensions.dart';
import 'package:paperless_mobile/core/text_style/app_text_style.dart';
import 'package:paperless_mobile/features/login/model/client_certificate.dart';
import 'package:paperless_mobile/features/login/model/login_form_credentials.dart';
import 'package:paperless_mobile/features/login/model/reachability_status.dart';
import 'package:paperless_mobile/features/login/view/widgets/form_fields/client_certificate_form_field.dart';
import 'package:paperless_mobile/features/login/view/widgets/form_fields/server_address_form_field.dart';
import 'package:paperless_mobile/features/login/view/widgets/form_fields/user_credentials_form_field.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';
import 'package:paperless_mobile/helpers/message_helpers.dart';
import 'package:paperless_mobile/routing/routes/app_logs_route.dart';

class AddAccountPage extends StatefulWidget {
  final FutureOr<void> Function(
    BuildContext context,
    String username,
    String password,
    String serverUrl,
    ClientCertificate? clientCertificate,
  ) onSubmit;

  final String? initialServerUrl;
  final String? initialUsername;
  final String? initialPassword;
  final ClientCertificate? initialClientCertificate;

  final String submitText;
  final String titleText;
  final bool showLocalAccounts;
  final Widget? bottomLeftButton;

  const AddAccountPage({
    super.key,
    required this.onSubmit,
    required this.submitText,
    required this.titleText,
    this.showLocalAccounts = false,
    this.initialServerUrl,
    this.initialUsername,
    this.initialPassword,
    this.initialClientCertificate,
    this.bottomLeftButton,
  });

  @override
  State<AddAccountPage> createState() => _AddAccountPageState();
}

class _AddAccountPageState extends State<AddAccountPage> {
  final _formKey = GlobalKey<FormBuilderState>();
  bool _isCheckingConnection = false;
  ReachabilityStatus _reachabilityStatus = ReachabilityStatus.unknown;
  bool _isFormSubmitted = false;
  bool _isContinue = true;

  final TextEditingController _serverController = TextEditingController();
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _passController = TextEditingController();

  final _pageController = PageController();

  @override
  void initState() {
    super.initState();
    _usernameController.addListener(_onTextChanged);
    _passController.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    _usernameController.removeListener(_onTextChanged);
    _passController.removeListener(_onTextChanged);
    _usernameController.dispose();
    _passController.dispose();
    _serverController.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;
    return Scaffold(
      appBar: AppBar(
        iconTheme: const IconThemeData(color: AppColor.primary),
        backgroundColor: Colors.transparent,
      ),
      // resizeToAvoidBottomInset: false,
      backgroundColor: AppColor.backgroundColor,
      body: SafeArea(
        bottom: false,
        child: SingleChildScrollView(
          child: FormBuilder(
            key: _formKey,
            child: AutofillGroup(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Gap(42),
                  SizedBox(
                      height: 120,
                      width: 120,
                      child: Image.asset('assets/logos/logo_and_name.png')),
                  const Gap(36),
                  Container(
                    height: 40,
                    width: size.width - 64,
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(62)),
                    child: Row(
                      children: [
                        GestureDetector(
                          onTap: () {
                            setState(() {
                              _isContinue = true;
                            });
                          },
                          child: Container(
                            height: 40,
                            width: (size.width - 64) / 2,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                                color: _isContinue
                                    ? AppColor.primary
                                    : Colors.white,
                                borderRadius: BorderRadius.circular(62)),
                            child: Text(
                              S.of(context)!.connection,
                              style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: _isContinue
                                      ? Colors.white
                                      : AppColor.primary),
                            ),
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            // setState(() {
                            //   _isContinue = false;
                            // });
                          },
                          child: Container(
                            height: 40,
                            width: (size.width - 64) / 2,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                                color: _isContinue
                                    ? Colors.white
                                    : AppColor.primary,
                                borderRadius: BorderRadius.circular(62)),
                            child: Text(
                              S.of(context)!.credentials,
                              style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: _isContinue
                                      ? AppColor.primary
                                      : Colors.white),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Gap(32),
                  _isContinue
                      ? SizedBox(height: 300, child: _buildContinue(size))
                      : SizedBox(height: 300, child: _buildCredentials(size)),
                  // const Spacer(),
                  const Gap(100),
                  Text(
                    'Copyright © 2025 DT Handel & Consulting GmbH',
                    style: AppTextStyles.textStyle12
                        .copyWith(color: AppColor.grey_8E8E93.withOpacity(0.4)),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        S.of(context)!.version(packageInfo.version),
                        style: AppTextStyles.textStyle12.copyWith(
                            color: AppColor.grey_8E8E93.withOpacity(0.4)),
                      ),
                      Text(
                        ' www.i-papierlos.de',
                        style: AppTextStyles.textStyle12
                            .copyWith(color: AppColor.primary),
                      ),
                    ],
                  ),
                  const Gap(30)
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContinue(Size size) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 6),
          child: ServerAddressFormField(
            textEditingController: _serverController,
            onChanged: (value) {
              setState(() {
                _reachabilityStatus = ReachabilityStatus.unknown;
              });
            },
          ).paddedSymmetrically(
            horizontal: 12,
            vertical: 12,
          ),
        ),
        const Gap(8),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              SvgPicture.asset('assets/svgs/iconInfo.svg'),
              const Gap(8),
              SizedBox(
                width: size.width - 64,
                child: Text(
                  S.of(context)!.infoConnection,
                  style: const TextStyle(color: AppColor.grey_909090),
                ),
              )
            ],
          ),
        ),
        const Gap(24),
        GestureDetector(
          onTap: () async {
            if (_serverController.text.isNotEmpty) {
              final status = await _updateReachability();
              _isContinue = false;
              setState(() {});
            }
          },
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            height: 48,
            width: size.width,
            alignment: Alignment.center,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: _serverController.text.isNotEmpty
                    ? AppColor.primary
                    : AppColor.grey_DADADA),
            child: Text(
              S.of(context)!.continueLabel,
              style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: _serverController.text.isNotEmpty
                      ? Colors.white
                      : AppColor.grey_A1A1A1),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCredentials(Size size) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // const Gap(32),
        // const Padding(
        //   padding: EdgeInsets.only(left: 16),
        //   child: Text(
        //     'MORE',
        //     style: TextStyle(
        //         fontWeight: FontWeight.w600, color: AppColor.grey_909090),
        //   ),
        // ),
        // const Gap(8),
        // Container(
        //   height: 48,
        //   width: size.width,
        //   padding: const EdgeInsets.only(left: 14),
        //   margin: const EdgeInsets.symmetric(horizontal: 16),
        //   decoration: BoxDecoration(
        //       color: Colors.white, borderRadius: BorderRadius.circular(10)),
        //   child: Row(
        //     children: [
        //       SvgPicture.asset('assets/svgs/upDown.svg'),
        //       const Text(
        //         'Username & Password',
        //         style: TextStyle(
        //             color: AppColor.primary, fontWeight: FontWeight.w600),
        //       )
        //     ],
        //   ),
        // ),
        // const Gap(10),
        // const Padding(
        //   padding: EdgeInsets.symmetric(horizontal: 16),
        //   child: Text(
        //     'Login with a username and password. This only works with local accounts, and not with accounts authenticated through OAuth2/OIDC.',
        //     style: TextStyle(color: AppColor.grey_909090),
        //   ),
        // ),
        // const Gap(24),
        Padding(
          padding: const EdgeInsets.only(left: 16),
          child: Text(
            S.of(context)!.credentials.toUpperCase(),
            style: const TextStyle(
                fontWeight: FontWeight.w600, color: AppColor.grey_909090),
          ),
        ),
        const Gap(8),
        Padding(
          padding: const EdgeInsets.only(left: 16, right: 16),
          child: UserCredentialsFormField(
            formKey: _formKey,
            passController: _passController,
            usernameController: _usernameController,
            initialUsername: widget.initialUsername,
            initialPassword: widget.initialPassword,
          ),
        ),
        const Gap(8),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              SvgPicture.asset('assets/svgs/iconInfo.svg'),
              const Gap(8),
              SizedBox(
                width: size.width - 64,
                child: Text(
                  S.of(context)!.yourPasswordIsOnlyUsedToLogin,
                  style: const TextStyle(color: AppColor.grey_909090),
                ),
              )
            ],
          ),
        ),
        const Gap(24),
        GestureDetector(
          onTap: () {
            _onSubmit();
          },
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            height: 48,
            width: size.width,
            alignment: Alignment.center,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: validate() ? AppColor.primary : AppColor.grey_DADADA),
            child: Text(
              S.of(context)!.login,
              style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: validate() ? Colors.white : AppColor.grey_A1A1A1),
            ),
          ),
        ),
      ],
    );
  }

  bool validate() {
    if (_usernameController.text.isNotEmpty &&
        _passController.text.isNotEmpty) {
      return true;
    } else {
      return false;
    }
  }

  Future<ReachabilityStatus> _updateReachability([String? address]) async {
    setState(() {
      _isCheckingConnection = true;
    });
    final selectedCertificate =
        _formKey.currentState?.getRawValue<ClientCertificate>(
      ClientCertificateFormField.fkClientCertificate,
    );
    final status = await context
        .read<ConnectivityStatusService>()
        .isPaperlessServerReachable(
          address ??
              _formKey.currentState!
                  .getRawValue(ServerAddressFormField.fkServerAddress),
          selectedCertificate,
        );
    setState(() {
      _isCheckingConnection = false;
      _reachabilityStatus = status;
    });
    return status;
  }

  Widget _buildStatusIndicator() {
    Widget buildIconText(
      IconData icon,
      String text, [
      Color? color,
    ]) {
      return ListTile(
        title: Text(
          text,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(color: color),
        ),
        leading: Icon(
          icon,
          color: color,
        ),
      );
    }

    Color errorColor = Theme.of(context).colorScheme.error;
    switch (_reachabilityStatus) {
      case ReachabilityStatus.notReachable:
        return buildIconText(
          Icons.close,
          S.of(context)!.couldNotEstablishConnectionToTheServer,
          errorColor,
        );
      case ReachabilityStatus.unknownHost:
        return buildIconText(
          Icons.close,
          S.of(context)!.hostCouldNotBeResolved,
          errorColor,
        );
      case ReachabilityStatus.missingClientCertificate:
        return buildIconText(
          Icons.close,
          S.of(context)!.loginPageReachabilityMissingClientCertificateText,
          errorColor,
        );
      case ReachabilityStatus.invalidClientCertificateConfiguration:
        return buildIconText(
          Icons.close,
          S.of(context)!.incorrectOrMissingCertificatePassphrase,
          errorColor,
        );
      case ReachabilityStatus.connectionTimeout:
        return buildIconText(
          Icons.close,
          S.of(context)!.connectionTimedOut,
          errorColor,
        );
      default:
        return const ListTile();
    }
  }

  Future<void> _onSubmit() async {
    FocusScope.of(context).unfocus();
    setState(() {
      _isFormSubmitted = true;
    });
    if (_formKey.currentState?.saveAndValidate() ?? false) {
      final form = _formKey.currentState!.value;
      final clientCertFormModel =
          form[ClientCertificateFormField.fkClientCertificate]
              as ClientCertificate?;

      final credentials =
          form[UserCredentialsFormField.fkCredentials] as LoginFormCredentials;
      try {
        await widget.onSubmit(
          context,
          credentials.username!,
          credentials.password!,
          form[ServerAddressFormField.fkServerAddress],
          clientCertFormModel,
        );
      } on PaperlessApiException catch (error) {
        showErrorMessage(context, error);
      } on ServerMessageException catch (error) {
        showLocalizedError(context, error.message);
      } on InfoMessageException catch (error) {
        showInfoMessage(context, error);
      } catch (error) {
        showGenericError(context, error);
      } finally {
        setState(() {
          _isFormSubmitted = false;
        });
      }
    }
  }
}
