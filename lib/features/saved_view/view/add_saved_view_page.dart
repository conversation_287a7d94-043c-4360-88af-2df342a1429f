import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:paperless_api/paperless_api.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/core/text_style/app_text_style.dart';
import 'package:paperless_mobile/core/widgets/hint_card.dart';
import 'package:paperless_mobile/features/saved_view/cubit/saved_view_cubit.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';

const _fkName = 'name';
const _fkShowOnDashboard = 'show_on_dashboard';
const _fkShowInSidebar = 'show_in_sidebar';

class AddSavedViewPage extends StatefulWidget {
  final DocumentFilter? initialFilter;
  final bool? showOnDashboard;
  final bool? showInSidebar;
  const AddSavedViewPage({
    super.key,
    this.initialFilter,
    this.showOnDashboard,
    this.showInSidebar,
  });

  @override
  State<AddSavedViewPage> createState() => _AddSavedViewPageState();
}

class _AddSavedViewPageState extends State<AddSavedViewPage> {
  final _savedViewFormKey = GlobalKey<FormBuilderState>();
  final _nameController = TextEditingController();
  late final SavedViewCubit _cubit;

  @override
  void initState() {
    super.initState();
    _cubit = context.read<SavedViewCubit>();
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;
    return Scaffold(
      appBar: AppBar(
        iconTheme: const IconThemeData(color: AppColor.primary),
        title: Row(
          children: [
            Text(S.of(context)!.newView, style: AppTextStyles.textStyleAppBar),
          ],
        ),
      ),
      // floatingActionButton: FloatingActionButton.extended(
      //   heroTag: "fab_add_saved_view_page",
      //   icon: const Icon(Icons.add),
      //   onPressed: () => _onCreate(context),
      //   label: Text(S.of(context)!.create),
      // ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            const Gap(8),
            FormBuilder(
              key: _savedViewFormKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    S.of(context)!.viewName,
                    style: const TextStyle(fontWeight: FontWeight.w600),
                  ),
                  const Gap(4),
                  ClipRRect(
                    borderRadius: BorderRadius.circular(10),
                    child: SizedBox(
                      height: 46,
                      child: TextFormField(
                        controller: _nameController,
                        validator: (value) {
                          if (value?.trim().isEmpty ?? true) {
                            return S.of(context)!.thisFieldIsRequired;
                          }
                          return null;
                        },
                        decoration: InputDecoration(
                          contentPadding: const EdgeInsets.symmetric(
                              vertical: 12, horizontal: 14),
                          fillColor: AppColor.white,
                          filled: true,
                          border: InputBorder.none,
                          hintText: S.of(context)!.enterViewName,
                        ),
                      ),
                    ),
                  ),
                  const Gap(8),
                  FormBuilderField<bool>(
                    name: _fkShowOnDashboard,
                    initialValue: widget.showOnDashboard ?? false,
                    builder: (field) {
                      return Row(
                        children: [
                          Checkbox(
                            value: field.value,
                            onChanged: (value) => field.didChange(value),
                            activeColor: AppColor.primary,
                          ),
                          Text(S.of(context)!.showOnDashboard),
                        ],
                      );
                    },
                  ),
                  FormBuilderField<bool>(
                    name: _fkShowInSidebar,
                    initialValue: widget.showInSidebar ?? false,
                    builder: (field) {
                      return Row(
                        children: [
                          Checkbox(
                            value: field.value,
                            onChanged: (value) => field.didChange(value),
                            activeColor: AppColor.primary,
                          ),
                          Text(S.of(context)!.showInSidebar),
                        ],
                      );
                    },
                  ),
                ],
              ),
            ),
            const Gap(30),
            GestureDetector(
              onTap: () {
                _onCreate(context);
              },
              child: Container(
                height: 48,
                width: size.width,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: AppColor.primary,
                ),
                child: Text(
                  S.of(context)!.createView,
                  style: const TextStyle(
                      color: AppColor.white, fontWeight: FontWeight.w600),
                ),
              ),
            ),
            const Gap(30),
            HintCard(
              hintText: S.of(context)!.hinTextCreateView,
            ),
          ],
        ),
      ),
    );
  }

  void _onCreate(BuildContext context) async {
    if (_savedViewFormKey.currentState?.saveAndValidate() ?? false) {
      var savedView = SavedView.fromDocumentFilter(
        widget.initialFilter ?? const DocumentFilter(),
        name: _nameController.text,
        showOnDashboard:
            _savedViewFormKey.currentState?.value[_fkShowOnDashboard] as bool,
        showInSidebar:
            _savedViewFormKey.currentState?.value[_fkShowInSidebar] as bool,
      );
      final router = GoRouter.of(context);
      await _cubit.add(
        savedView,
      );
      router.pop();
    }
  }
}
