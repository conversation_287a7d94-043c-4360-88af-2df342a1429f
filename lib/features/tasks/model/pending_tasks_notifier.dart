import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:paperless_api/paperless_api.dart';
import 'package:paperless_mobile/core/Color/app_color.dart';
import 'package:paperless_mobile/features/home/<USER>/bottom_nav_bar_cubit.dart';
import 'package:paperless_mobile/generated/l10n/app_localizations.dart';
import 'package:paperless_mobile/routing/navigation_keys.dart';
import 'package:paperless_mobile/routing/routes/documents_route.dart';
import 'package:paperless_mobile/routing/routes/shells/authenticated_route.dart';

class PendingTasksNotifier extends ValueNotifier<Map<String, Task>> {
  final PaperlessTasksApi _api;

  final Map<String, StreamSubscription> _subscriptions = {};

  PendingTasksNotifier(this._api) : super({});

  @override
  void dispose() {
    stopListeningToTaskChanges();
    super.dispose();
  }

  void listenToTaskChanges(
    String taskId, {
    Map<String, dynamic>? param,
    List<int>? projects,
  }) {
    final sub = _api
        .listenForTaskChanges(taskId, param: param, projects: projects ?? [])
        .listen(
      (task) {
        if (value.containsKey(taskId)) {
          final oldTask = value[taskId]!;
          if (oldTask.status != task.status) {
            // Only notify of changes if task status has changed...
            value = {...value, taskId: task};
            notifyListeners();
            print(task.relatedDocument);

            // Sử dụng rootNavigatorKey để hiển thị dialog từ bất kỳ đâu trong app
            final navigatorContext = rootNavigatorKey.currentContext;
            if (navigatorContext != null) {
              showDialog(
                  context: navigatorContext,
                  builder: (dialogContext) => AlertDialog(
                        surfaceTintColor: Colors.transparent,
                        title: Text(S.of(navigatorContext)!.uploadCompleted),
                        content: Text(S
                            .of(navigatorContext)!
                            .yourDocumentsHaveBeenSuccessfullyUploaded),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.pop(dialogContext),
                            child: Text(S.of(navigatorContext)!.close),
                          ),
                          ElevatedButton(
                              onPressed: () {
                                Navigator.pop(dialogContext);
                                DocumentDetailsRoute(
                                  title: task.taskFileName,
                                  id: int.parse(task.relatedDocument!),
                                ).push(navigatorContext);
                              },
                              style: ElevatedButton.styleFrom(
                                  backgroundColor: AppColor.primary),
                              child: Text(
                                S.of(dialogContext)!.open,
                                style: const TextStyle(color: AppColor.white),
                              ))
                        ],
                      ));
            }
          }
        } else {
          value = {...value, taskId: task};
          notifyListeners();
        }
      },
    );
    sub
      ..onDone(() {
        sub.cancel();
        value = value..remove(taskId);
        notifyListeners();
      })
      ..onError((_) {
        sub.cancel();
        value = value..remove(taskId);
        notifyListeners();
      });

    _subscriptions.putIfAbsent(taskId, () => sub);
  }

  void stopListeningToTaskChanges([String? taskId]) {
    if (taskId != null) {
      _subscriptions[taskId]?.cancel();
      _subscriptions.remove(taskId);
    } else {
      for (var sub in _subscriptions.values) {
        sub.cancel();
      }
      _subscriptions.clear();
    }
  }

  Future<void> acknowledgeTasks(Iterable<String> taskIds) async {
    final tasks = value.values.where((task) => taskIds.contains(task.taskId));
    await Future.wait([for (var task in tasks) _api.acknowledgeTask(task)]);
    value = value..removeWhere((key, value) => taskIds.contains(key));
    notifyListeners();
  }
}
