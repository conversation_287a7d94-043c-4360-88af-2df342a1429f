import 'package:dio/dio.dart';
import 'package:paperless_api/src/models/paperless_api_exception.dart';

extension DioExceptionUnravelExtension on DioException {
  Object unravel({Object? orElse}) {
    // If there's a specific error, return it
    if (error != null) {
      return error!;
    }

    // If there's a provided fallback, use it
    if (orElse != null) {
      return orElse;
    }

    // Create a more informative exception based on DioException details
    String details = "DioException occurred";

    if (message != null && message!.isNotEmpty) {
      details = message!;
    } else {
      // Build details from available information
      final parts = <String>[];

      if (response?.statusCode != null) {
        parts.add("Status: ${response!.statusCode}");
      }

      if (response?.statusMessage != null) {
        parts.add("Message: ${response!.statusMessage}");
      }

      if (requestOptions.path.isNotEmpty) {
        parts.add("Path: ${requestOptions.path}");
      }

      parts.add("Type: ${type.name}");

      if (parts.isNotEmpty) {
        details = parts.join(", ");
      }
    }

    return PaperlessApiException.unknown(
      details: details,
      httpStatusCode: response?.statusCode,
    );
  }
}
