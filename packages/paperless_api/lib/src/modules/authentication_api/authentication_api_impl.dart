import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:paperless_api/paperless_api.dart';
import 'package:paperless_api/src/extensions/dio_exception_extension.dart';

class PaperlessAuthenticationApiImpl implements PaperlessAuthenticationApi {
  final Dio client;

  PaperlessAuthenticationApiImpl(this.client);

  @override
  Future<String> login({
    required String username,
    required String password,
  }) async {
    try {
      // Debug: Log the base URL being used
      debugPrint(
          "DEBUG: Making login request to: ${client.options.baseUrl}/api/token/");
      debugPrint("DEBUG: Headers: ${client.options.headers}");

      final response = await client.post(
        "/api/token/",
        data: {
          "username": username,
          "password": password,
        },
        options: Options(
          sendTimeout: const Duration(seconds: 5),
          receiveTimeout: const Duration(seconds: 5),
          followRedirects: false,
          headers: {
            "Accept": "application/json",
          },
          // validateStatus: (status) {
          //   return status! == 200;
          // },
        ),
      );

      debugPrint("DEBUG: Login response status: ${response.statusCode}");
      debugPrint("DEBUG: Login response data: ${response.data}");

      if (response.data is Map<String, dynamic> &&
          response.data['token'] != null) {
        return response.data['token'];
      } else {
        throw const PaperlessApiException.unknown(
          details: "Invalid response format: token not found in response",
        );
      }
      // } else if (response.statusCode == 302) {
      // final redirectUrl = response.headers.value("location");
      // return AuthenticationTemporaryRedirect(redirectUrl!);
    } on DioException catch (exception) {
      debugPrint("DEBUG: DioException in login: ${exception.toString()}");
      debugPrint("DEBUG: DioException type: ${exception.type}");
      debugPrint("DEBUG: DioException message: ${exception.message}");
      debugPrint("DEBUG: DioException error: ${exception.error}");
      debugPrint("DEBUG: DioException response: ${exception.response}");
      throw exception.unravel();
    } catch (error, stackTrace) {
      debugPrint("DEBUG: Unexpected error in login: ${error.toString()}");
      throw PaperlessApiException.unknown(
        details: error.toString(),
        stackTrace: stackTrace,
      );
    }
  }
}
