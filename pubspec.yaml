name: paperless_mobile
description: An (almost) fully fledged mobile paperless-ng*x client.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.0.0+1

environment:
  sdk: ">=3.1.0 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  permission_handler: ^11.0.1
  pdf: ^3.10.2
  path_provider: ^2.0.10
  image: ^4.0.17
  photo_view: ^0.14.0
  intl: ^0.19.0
  flutter_svg: ^2.0.7
  file_picker: ^6.1.1
  web_socket_channel: ^2.2.0
  http: ^1.1.2
  flutter_cache_manager: ^3.3.0
  cached_network_image: ^3.2.1
  shimmer: ^3.0.0
  flutter_bloc: ^8.1.1
  equatable: ^2.0.7
  flutter_form_builder: ^9.1.1
  package_info_plus: ^5.0.1
  local_auth: ^2.1.2
  connectivity_plus: ^5.0.2
  share_plus: ^7.2.1
  introduction_screen: ^3.0.2
  receive_sharing_intent: ^1.4.5
  uuid: ^4.1.0
  flutter_typeahead: ^5.0.1
  fluttertoast: ^8.1.1
  paperless_api:
    path: packages/paperless_api
  hive: ^2.2.3
  rxdart: ^0.27.7
  badges: ^3.1.2
  flutter_colorpicker: ^1.0.3
  provider: ^6.0.5
  dio: ^5.0.0
  hydrated_bloc: ^9.0.0
  json_annotation: ^4.8.1
  pretty_dio_logger: ^1.2.0-beta-1
  collection: ^1.17.0
  device_info_plus: ^9.0.3
  flutter_local_notifications: ^16.3.0
  responsive_builder: ^0.7.0
  open_filex: ^4.3.2
  flutter_displaymode: ^0.6.0
  dynamic_color: ^1.5.4
  flutter_html: ^3.0.0-alpha.6
  freezed_annotation: ^2.4.1
  animations: ^2.0.7
  hive_flutter: ^1.1.0
  flutter_secure_storage: ^9.0.0
  sliver_tools: ^0.2.10
  webview_flutter: ^4.2.1
  go_router: ^13.0.1
  fl_chart: ^0.66.0
  palette_generator: ^0.3.3+2
  defer_pointer: ^0.0.2
  transparent_image: ^2.0.1
  flutter_animate: ^4.2.0+1
  shared_preferences: ^2.2.1
  flutter_markdown: ^0.6.18
  logger: ^2.0.2+1
  synchronized: ^3.1.0
  extended_masked_text: ^2.3.1
  font_awesome_flutter: ^10.6.0
  # camerawesome: ^2.0.0-dev.1
  pdfx:
    git:
      url: "https://github.com/ScerIO/packages.flutter"
      ref: "d637108a2a6e3e97a70304f00f1eda9511fb4f92"
      path: packages/pdfx
  markdown: ^7.1.1
  intl_utils: ^2.8.8
  gap: ^3.0.1
  curl_logger_dio_interceptor: ^1.0.0
  cunning_document_scanner: ^1.2.3
  dotted_border: ^2.1.0
  copy_with_extension_gen: ^5.0.4
  copy_with_extension: ^5.0.4
  gal: ^2.3.1
  printing: ^5.12.0

  # pdfrx:
  #   git:
  #     url: https://github.com/astubenbord/pdfrx
  #     ref: master

dependency_overrides:
  intl: ^0.18.1
  js: ^0.6.3

dev_dependencies:
  integration_test:
    sdk: flutter
  flutter_test:
    sdk: flutter
  build_runner: ^2.4.8
  bloc_test: ^9.1.0
  dependency_validator: ^3.0.0
  flutter_lints: ^3.0.1
  json_serializable: ^6.5.4
  freezed: ^2.4.1
  hive_generator: ^2.0.1
  mock_server:
    path: packages/mock_server
  go_router_builder: ^2.3.3
# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
# The following section is specific to Flutter.

flutter_gen:
  output: lib/core/gen/

flutter:
  # generate: true
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  generate: false

  assets:
    - assets/logos/
    - assets/images/
    - test/fixtures/
    - test/fixtures/documents/
    - test/fixtures/correspondents/
    - test/fixtures/tags/
    - test/fixtures/preview/
    - test/fixtures/document_types/
    - assets/changelogs/
    - assets/example/
    - assets/svgs/

  fonts:
    - family: RobotoMono
      fonts:
        - asset: assets/fonts/RobotoMono-Regular.ttf
